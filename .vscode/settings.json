{
  "typescript.preferences.importModuleSpecifierEnding": "auto",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "files.simpleDialog.enable": true,
  "javascript.preferences.quoteStyle": "single",
  "typescript.preferences.quoteStyle": "single",
  "search.useIgnoreFiles": true,
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true,
    "**/dist/**": true
  },
  "jest.autoRun": "off",
  // Enforce case sensitivity for imports
  "typescript.preferences.importModuleSpecifierCase": "preserve",
  "typescript.tsserver.useSyntaxServer": "never",
  "typescript.tsserver.watchOptions": {
    "watchFile": "priorityPollingInterval"
  },
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  }
}
