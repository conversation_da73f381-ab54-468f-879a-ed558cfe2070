---
description: 
globs: 
alwaysApply: true
---
@check  
Please read the entire codebase and check if the function, validator, filter, pipe, or class I’m about to write already exists. If it does, show me where it is and suggest improvements if needed.  

If it doesn’t exist, only then proceed to create it.  
Do not hallucinate or write redundant logic.  
Avoid rewriting what’s already working.  
Focus on clean reuse over reimplementation.
always add new functions or clasess or anuything new to the readme under each folder and also if you remove anything adjust it and edit it to reflect what you remove not like logs but more like memory of what we have 
avoid  Verifying the same condition multiple times in different places
please (Don't Repeat Yourself) - Having the same logic repeated unnecessarily
please avoid "Guard clause redundancy" - When multiple early-return safety checks test for the same condition
please avoid "Paranoid programming" - Excessive defensive checks beyond what's necessary
always use Promise.all() with async/await Whenever you need to run multiple asynchronous tasks in parallel and wait for all of them to complete.
always use @Param('userId') @Trim() userId: string,  correctly 
dont overwrite when i say  do exactly what i said 