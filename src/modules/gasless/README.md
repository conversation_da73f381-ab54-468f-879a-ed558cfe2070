# Gasless Module

## Overview
This module handles gasless transactions and related utilities.

## Services
- **GenerateAndSignEip7702Service**: A service for generating and signing EIP-7702 authorizations. It allows for dependency injection of PrivyService to manage wallet operations securely.

## Recent Updates
- Refactored `generateAndSignAuthorization` into an injectable class for better dependency management.
