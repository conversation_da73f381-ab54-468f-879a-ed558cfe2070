import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpendingLimit } from '../spendingLimit.entity';
import { getLimitTank } from '../handlers/getLimitTank';

@Injectable()
export class LimitTankFetchService {
  private readonly logger = new Logger(LimitTankFetchService.name);

  constructor(
    @InjectRepository(SpendingLimit)
    private readonly spendingLimitRepository: Repository<SpendingLimit>,
  ) { }

  /**
   * Aggregates spending limits for a user into a single tank view for the current day.
   * Returns total limit in fiat and USD, and percentage used for the day.
   * Resets daily at midnight based on a default timezone.
   * Accumulates new limits added on the current day.
   * @param userId - The ID of the user.
   * @returns An object containing total fiat limit, total USD limit, and percentage used for the day.
   */
  async getLimitTank(
    userId: string,
  ): Promise<{
    totalfiatLimit: number;
    totalUsdLimit: number;
    percentageUsed: number;
  }> {
    const defaultTimezone = 'UTC';
    this.logger.log(
      `Fetching limit tank data for user ${userId} in default timezone ${defaultTimezone}`,
    );
    return await getLimitTank(userId, defaultTimezone, this.spendingLimitRepository);
  }
}
