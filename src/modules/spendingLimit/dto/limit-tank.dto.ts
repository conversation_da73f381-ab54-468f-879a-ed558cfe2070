import { ApiProperty } from '@nestjs/swagger';

export class LimitTankResponseDto {
    @ApiProperty({
        description: 'Total fiat limit for the day',
        example: 1000.00
    })
    totalfiatLimit: number;

    @ApiProperty({
        description: 'Total USD limit for the day',
        example: 1000.00
    })
    totalUsdLimit: number;

    @ApiProperty({
        description: 'Percentage of limit used for the day',
        example: 25.5
    })
    percentageUsed: number;
}
