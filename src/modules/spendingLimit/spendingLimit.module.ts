import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SpendingLimit } from './spendingLimit.entity';
import { SpendingLimitController } from './spendingLimit.controller';
import { SetLimitService } from './services/setLimit.service';
import { UserModule } from '../user/user.module';
import { OfframpModule } from '../offramp/offramp.module';
import { ConfigModule } from '@nestjs/config';
import { User } from '../user/entity/user.entity';
import { Offramp } from '../offramp/offramp.entity';
import { FundsLock } from '../Card/entity/fundsLock.entity';
import { LimitTankFetchService } from './services/limitTankFetch.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([SpendingLimit, User, Offramp, FundsLock]),
    UserModule,
    OfframpModule,
    ConfigModule,
  ],
  exports: [TypeOrmModule, SetLimitService, LimitTankFetchService],
  controllers: [SpendingLimitController],
  providers: [SetLimitService, LimitTankFetchService],
})
export class SpendingLimitModule { }
