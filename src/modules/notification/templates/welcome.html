<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to zerocard</title>
    <!--[if mso]>
    <style type="text/css">
        body, table, td {font-family: Arial, Helvetica, sans-serif !important;}
    </style>
    <![endif]-->
    <style type="text/css">
        /* Reset styles */
        body, p, td, div, h1, h2, h3 {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Rounded', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            -webkit-font-smoothing: antialiased;
            -webkit-text-size-adjust: 100%;
            width: 100% !important;
            height: 100% !important;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        
        .ReadMsgBody {width: 100%;}
        .ExternalClass {width: 100%;}
        .ExternalClass * {line-height: 100%;}
        
        table {
            border-spacing: 0;
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            border: 0;
            -ms-interpolation-mode: bicubic;
            display: block;
        }
        
        .wrapper {
            width: 100%;
            table-layout: fixed;
            background-color: #f9f9f9;
            padding: 20px 0;
        }
        
        .container {
            width: 100%;
            max-width: 580px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .header {
            font-weight: 500;
            font-size: 28px;
            line-height: 120%;
            color: #1F1F1F;
            padding: 0 25px 30px 25px;
        }
        
        .content {
            font-weight: 400;
            font-size: 16px;
            line-height: 150%;
            color: #6B6B6B;
            padding: 0 25px 30px 25px;
        }
        
        .footer {
            background-color: #F4F4F4;
            padding: 25px 15px;
            text-align: center;
        }
        
        .footer-link {
            font-weight: 500;
            font-size: 13px;
            line-height: 20px;
            color: #919191;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .footer-link-dark {
            color: #1F1F1F;
        }
        
        .divider {
            display: inline-block;
            width: 1px;
            height: 12px;
            background-color: #BABABA;
            margin: 0 5px;
            vertical-align: middle;
        }
        
        .icon {
            vertical-align: middle;
            margin-right: 4px;
            width: 14px;
            height: 14px;
        }
        
        .spacer {
            height: 40px;
        }
        
        @media screen and (max-width: 480px) {
            .container {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .header {
                font-size: 22px;
                padding: 0 15px 20px 15px;
            }
            
            .content {
                font-size: 14px;
                padding: 0 15px 25px 15px;
            }
            
            .spacer {
                height: 30px !important;
            }
            
            .mobile-stack {
                display: block !important;
                width: 100% !important;
                max-width: 100% !important;
                direction: ltr !important;
            }
            
            .mobile-center {
                text-align: center !important;
            }
            
            .mobile-padding {
                padding: 15px !important;
            }
            
            .mobile-spacing {
                padding-top: 8px !important;
            }
            
            .logo-container {
                text-align: center !important;
            }
            
            .email-container {
                text-align: center !important;
                padding-top: 10px !important;
            }
            
            .footer-links {
                text-align: center !important;
                display: block !important;
            }
            
            .divider {
                display: none !important;
            }
            
            .logo-img {
                width: 120px !important;
                height: auto !important;
                margin: 0 auto 10px auto !important;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <table role="presentation" class="container" cellpadding="0" cellspacing="0" width="580" align="center">
            <!-- Header Section with Logo and Email in same row -->
            <tr>
                <td class="mobile-padding" style="padding: 25px;">
                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td class="mobile-stack" style="width: 150px; vertical-align: middle;">
                                <img class="logo-img" src="https://i.postimg.cc/5yvwZq2k/zerologopng.png" alt="Zerocard logo" width="150" height="auto" style="display: block; border-radius: 8px;">
                            </td>
                            <td class="mobile-stack email-container" style="text-align: right; vertical-align: middle;">
                                <div style="font-weight: 400; font-size: 16px; line-height: 120%; color: #6B6B6B;"><EMAIL></div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            
            <!-- Spacer between logo row and welcome heading -->
            <tr>
                <td class="spacer" style="height: 40px; font-size: 0; line-height: 0;">&nbsp;</td>
            </tr>
            
            <!-- Welcome Header with line break -->
            <tr>
                <td class="header" style="font-weight: 500; font-size: 28px; line-height: 120%; color: #1F1F1F; padding: 0 25px 30px 25px;">
                    Welcome to zerocard<br>mon ami!
                </td>
            </tr>
            
            <!-- Main Content -->
            <tr>
                <td class="content" style="font-weight: 400; font-size: 16px; line-height: 150%; color: #6B6B6B; padding: 0 25px 30px 25px;">
                    Welcome aboard! We're thrilled to have you join the Zerocard family. 🎉<br><br>
                    
                    With Zerocard, you're not just getting a debit card; you're unlocking a whole new way to spend your crypto. Whether you're grabbing a coffee, shopping online, or withdrawing cash from an ATM, your Zerocard has got you covered.<br><br>
                    
                    <strong>Here's what you can look forward to:</strong><br><br>
                    
                    <strong>Seamless Spending:</strong> Use your crypto just like cash, anywhere debit cards are accepted.<br><br>
                    
                    <strong>Total Control:</strong> Set spending limits, track your transactions, and manage your card all from our sleek app.<br><br>
                    
                    <strong>Security First:</strong> Your funds are safe with us, thanks to top-notch security measures.<br><br>
                    
                    Ready to get started? Your first step is to order your Zerocard. Just head over to the app and follow the prompts. It's that easy!<br><br>
                    
                    The Zerocard Team
                </td>
            </tr>
            
            <!-- Footer -->
            <tr>
                <td class="footer" style="background-color: #F4F4F4; padding: 25px 15px; text-align: center;">
                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%">
                        <!-- First row of footer links -->
                        <tr>
                            <td align="center">
                                <a href="#" class="footer-link" style="font-weight: 500; font-size: 13px; line-height: 20px; color: #919191; text-decoration: none; display: inline-block; margin: 5px;">Terms of use</a>
                                
                                <span class="divider" style="display: inline-block; width: 1px; height: 12px; background-color: #BABABA; margin: 0 5px; vertical-align: middle;"></span>
                                
                                <a href="#" class="footer-link" style="font-weight: 500; font-size: 13px; line-height: 20px; color: #919191; text-decoration: none; display: inline-block; margin: 5px;">
                                    <svg class="icon" width="14" height="14" viewBox="0 0 61 62" fill="none" xmlns="http://www.w3.org/2000/svg" style="vertical-align: middle; margin-right: 4px;">
                                        <path d="M30.5664 3.37988C37.7388 3.37988 42.8656 3.3854 46.7617 3.90918C50.3468 4.39118 52.5576 5.27579 54.1895 6.75391L54.5088 7.05664C56.1707 8.71862 57.1421 10.9797 57.6562 14.8037C58.18 18.7 58.1855 23.8273 58.1855 31C58.1855 38.1723 58.18 43.2991 57.6562 47.1953C57.1743 50.7801 56.2904 52.9912 54.8125 54.623L54.5088 54.9424C52.8467 56.6043 50.5857 57.5757 46.7617 58.0898C42.8656 58.6136 37.7388 58.6191 30.5664 58.6191C23.3938 58.6191 18.2664 58.6136 14.3701 58.0898C10.7852 57.6079 8.57414 56.724 6.94238 55.2461L6.62305 54.9424C4.96115 53.2803 3.9897 51.0193 3.47559 47.1953C2.95179 43.2991 2.94629 38.1723 2.94629 31C2.94629 23.8273 2.95177 18.7 3.47559 14.8037C3.9576 11.2185 4.84214 9.00777 6.32031 7.37598L6.62305 7.05664C8.28503 5.39467 10.5461 4.42331 14.3701 3.90918C18.2664 3.38537 23.3938 3.37988 30.5664 3.37988Z" stroke="#919191" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M14.709 46.8569L28.0095 33.5567M28.0095 33.5567L14.709 15.1406H23.5191L33.125 28.4408M28.0095 33.5567L37.6151 46.8569H46.4252L33.125 28.4408M46.4252 15.1406L33.125 28.4408" stroke="#919191" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    X (Twitter)
                                </a>
                            </td>
                        </tr>
                        
                        <!-- Second row of footer links -->
                        <tr>
                            <td align="center" class="mobile-spacing" style="padding-top: 10px;">
                                <a href="#" class="footer-link" style="font-weight: 500; font-size: 13px; line-height: 20px; color: #919191; text-decoration: none; display: inline-block; margin: 5px;">
                                    <svg class="icon" width="14" height="14" viewBox="0 0 61 62" fill="none" xmlns="http://www.w3.org/2000/svg" style="vertical-align: middle; margin-right: 4px;">
                                        <path d="M55.5006 29.7442V39.7877C55.5006 48.5757 50.4789 52.342 42.9462 52.342H17.8376C10.3049 52.342 5.2832 48.5757 5.2832 39.7877V22.2116C5.2832 13.4235 10.3049 9.65723 17.8376 9.65723H30.3919" stroke="#919191" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M17.8379 23.4668L25.6969 29.744C28.2831 31.8029 32.5265 31.8029 35.1127 29.744" stroke="#919191" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M49.1736 7.9501L49.8767 9.38127C50.2282 10.0843 51.107 10.7372 51.8853 10.8878L52.8395 11.0385C55.7019 11.5155 56.3798 13.6247 54.3209 15.7087L53.442 16.5875C52.8645 17.1901 52.5382 18.3451 52.714 19.1486L52.8395 19.6759C53.6178 23.1409 51.7849 24.4716 48.7719 22.6638L48.119 22.2871C47.3406 21.8352 46.0852 21.8352 45.3069 22.2871L44.654 22.6638C41.6159 24.4967 39.7829 23.1409 40.5864 19.6759L40.7119 19.1486C40.8877 18.3451 40.5613 17.1901 39.9838 16.5875L39.105 15.7087C37.0461 13.6247 37.724 11.5155 40.5864 11.0385L41.5406 10.8878C42.2938 10.7623 43.1977 10.0843 43.5492 9.38127L44.2523 7.9501C45.6081 5.21325 47.8177 5.21325 49.1736 7.9501Z" stroke="#919191" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    Reach out to us
                                </a>
                                
                                <span class="divider" style="display: inline-block; width: 1px; height: 12px; background-color: #BABABA; margin: 0 5px; vertical-align: middle;"></span>
                                
                                <a href="#" class="footer-link footer-link-dark" style="font-weight: 500; font-size: 13px; line-height: 20px; color: #1F1F1F; text-decoration: none; display: inline-block; margin: 5px;">
                                    Reserve a card
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
