import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * Interface defining the parameters required for Smile ID token request.
 */
export interface SmileIdTokenParams {
    partnerId: string;
    defaultCallback: string;
    apiKey: string;
    sidServer: '0' | '1'; // '0' for sandbox, '1' for production
    userId: string;
    jobId: string;
    product: 'authentication' | 'basic_kyc' | 'smartselfie' | 'biometric_kyc' | 'enhanced_kyc' | 'doc_verification';
    callbackUrl?: string;
}

/**
 * Generates a web token for Smile ID verification.
 * @param params - The parameters for the Smile ID token request.
 * @returns Promise resolving to the generated web token.
 * @throws HttpException if the token generation fails.
 */
export async function generateSmileIdToken(params: SmileIdTokenParams): Promise<string> {
    try {
        const smileIdentityCore = require('smile-identity-core');
        const WebApi = smileIdentityCore.WebApi;

        // Initialize connection
        const connection = new WebApi(
            params.partnerId,
            params.defaultCallback,
            params.apiKey,
            params.sidServer
        );

        // Create request parameters
        const requestParams = {
            user_id: params.userId,
            job_id: params.jobId,
            product: params.product,
            ...(params.callbackUrl && { callback_url: params.callbackUrl }),
        };

        // Generate the web token
        const response = await connection.get_web_token(requestParams);

        if (!response || !response.token) {
            throw new HttpException(
                'Failed to generate Smile ID token: Token not found in response',
                HttpStatus.BAD_REQUEST,
            );
        }

        return response.token;
    } catch (error) {
        throw new HttpException(
            `Error generating Smile ID token: ${error instanceof Error ? error.message : 'Unknown error'}`,
            HttpStatus.INTERNAL_SERVER_ERROR,
        );
    }
}
