# compiled output
/dist
/node_modules
/build

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# temp directory
.temp
.tmp

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
.qodo

# Mock data
**/mock*/**
src/modules/offramp/handlers/mockofframOrderstatus
publickey.cer
privatekey.pem

# Mock JSON files for Card module
src/modules/Card/mock/createcustomer.json
src/modules/Card/mock/getAccount.json
src/modules/Card/mock/getAccountBalance.json
src/modules/Card/mock/getcard.json
src/modules/Card/mock/getTransfer.json
src/modules/Card/mock/mapcard.json
src/modules/Card/mock/offrampmock.json
src/modules/Card/mock/refund.json
src/modules/Card/mock/transferFunds.json
src/modules/Card/mock/updateCard.json
src/modules/Card/mock/updatecustomer.json
src/modules/Card/mock/validatekyc.json
src/modules/Card/mock/webhook.json


bash.sh
.env.prod
