{"compilerOptions": {"module": "NodeNext", "moduleResolution": "NodeNext", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false, "strict": true, "preserveWatchOutput": true}, "watchOptions": {"watchFile": "useFsEvents", "watchDirectory": "useFsEvents", "fallbackPolling": "dynamicPriority", "synchronousWatchDirectory": true, "excludeDirectories": ["**/node_modules", "**/dist"]}, "include": ["src/**/*", "test/**/*", "extractorderIDnotsureyet.ts"], "exclude": ["node_modules", "dist"]}