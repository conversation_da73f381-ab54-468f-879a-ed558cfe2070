{"name": "Zerocard-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "yarn clean && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:fix-imports": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix --quiet --rule 'unused-imports/no-unused-imports: error' --rule 'unused-imports/no-unused-vars: off'", "lint:ts": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix --quiet --rule '@typescript-eslint/consistent-type-imports: warn' --rule '@typescript-eslint/no-import-type-side-effects: warn'", "dev": "yarn build && yarn start:debug", "debug": "yarn lint:fix-imports && yarn start:debug", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js", "migration:generate": "DEBUG=* npx ts-node ./node_modules/typeorm/cli.js migration:generate -d src/config/db.config.ts src/migrations/NewMigration", "migration:run": "yarn typeorm migration:run -d src/config/db.config.ts", "migration:revert": "yarn typeorm migration:revert -d src/config/db.config.ts", "typecheck": "tsc --noEmit", "check-imports": "tsc --noEmit --strict --forceConsistentCasingInFileNames --diagnostics --listFiles --listEmittedFiles", "precheck": "yarn check-imports", "prepare": "husky"}, "dependencies": {"@biconomy/abstractjs": "^1.0.13", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "11.1.0", "@nestjs/swagger": "^11.0.2", "@nestjs/typeorm": "^11.0.0", "@privy-io/server-auth": "^1.22.1", "@rhinestone/module-sdk": "^0.2.8", "@types/moment-timezone": "^0.5.30", "@types/multer": "^1.4.12", "@zerodev/ecdsa-validator": "^5.4.5", "@zerodev/sdk": "^5.4.28", "axios": "^1.8.4", "body-parser": "^2.2.0", "bullmq": "^5.40.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudinary": "^2.5.1", "decimal.js": "^10.5.0", "dotenv": "^16.4.7", "ethers": "^6.13.5", "file-type": "^20.4.1", "ioredis": "^5.5.0", "moment-timezone": "^0.5.48", "moralis": "^2.27.2", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^3.0.3", "node-rsa": "^1.1.1", "nodemailer": "^6.10.0", "permissionless": "^0.2.42", "pg": "^8.13.1", "posthog-node": "^4.13.0", "pusher": "^5.2.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "smile-identity-core": "^3.1.0", "swagger-ui-express": "^5.0.1", "typeorm": "0.3.17", "viem": "^2.30.0", "web3": "^4.16.0", "web3-utils": "^4.3.3"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "11.1.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "cross-env": "^7.0.3", "eslint": "8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "jest": "^29.5.0", "jsencrypt": "^3.3.2", "lint-staged": "^16.0.0", "prettier": "^3.0.0", "rimraf": "^6.0.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "5.8.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=20.0.0"}, "lint-staged": {"*.ts": ["prettier --write", "eslint --fix"]}}